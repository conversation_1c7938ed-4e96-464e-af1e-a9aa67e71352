from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from app import db
from app.auth import auth_bp
from app.auth.forms import LoginForm, RegisterForm
from app.models import User, Role
from app.services.school_registration_service import SchoolRegistrationService
from urllib.parse import urlparse
from datetime import datetime

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user is None or not user.check_password(form.password.data):
            flash('用户名或密码错误', 'danger')
            return redirect(url_for('auth.login'))

        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            next_page = url_for('main.index')
        return redirect(next_page)

    return render_template('auth/login.html', title='登录', form=form, now=datetime.now())

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('您已成功退出登录', 'success')
    return redirect(url_for('main.index'))

@auth_bp.route('/guest-login')
def guest_login():
    """游客登录功能 - 共享账户方案"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    # 查找或创建共享的游客演示账户
    guest_user = User.query.filter_by(username='guest_demo').first()

    if not guest_user:
        # 创建共享的游客演示账户
        guest_user = User(
            username='guest_demo',
            email='<EMAIL>',
            real_name='游客演示账户',
            phone='13800000000',
            status=1
        )
        guest_user.set_password('demo123456')  # 设置默认密码

        try:
            db.session.add(guest_user)
            db.session.commit()

            # 为游客账户分配基础角色
            guest_role = Role.query.filter_by(name='游客用户').first()
            if not guest_role:
                # 创建游客角色
                guest_role = Role(
                    name='游客用户',
                    description='游客演示账户，具有基础查看权限',
                    permissions='{"*": ["view"]}'  # 只有查看权限
                )
                db.session.add(guest_role)
                db.session.commit()

            # 关联角色
            guest_user.roles.append(guest_role)
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            flash('创建游客账户失败，请稍后重试', 'danger')
            return redirect(url_for('main.index'))

    # 登录游客账户
    login_user(guest_user, remember=False)

    # 更新最后登录时间
    guest_user.last_login = datetime.now().replace(microsecond=0)
    try:
        db.session.commit()
    except:
        db.session.rollback()

    flash('欢迎使用游客体验模式！您可以浏览系统的各项功能', 'success')
    return redirect(url_for('main.index'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = RegisterForm()
    if form.validate_on_submit():
        try:
            # 检查学校名称是否已存在
            if SchoolRegistrationService.check_school_name_exists(form.school_name.data):
                flash('该学校名称已被使用，请更换一个', 'danger')
                return render_template('auth/register.html', title='注册', form=form, now=datetime.now())

            # 准备注册数据
            form_data = {
                'school_name': form.school_name.data,
                'username': form.username.data,
                'email': form.email.data,
                'real_name': form.real_name.data,
                'phone': form.phone.data,
                'password': form.password.data
            }

            # 使用学校注册服务创建学校和用户
            user, school_area = SchoolRegistrationService.register_school_and_user(form_data)

            # 自动登录用户
            login_user(user, remember=True)

            # 更新最后登录时间
            user.last_login = datetime.now()
            db.session.commit()

            flash(f'恭喜！学校 "{school_area.name}" 创建成功，您已成为该校管理员，欢迎使用系统！', 'success')

            # 重定向到首页
            next_page = request.args.get('next')
            if not next_page or urlparse(next_page).netloc != '':
                next_page = url_for('main.index')
            return redirect(next_page)

        except ValueError as e:
            flash(str(e), 'danger')
            return render_template('auth/register.html', title='注册', form=form, now=datetime.now())
        except Exception as e:
            flash(f'注册过程中发生错误，请稍后重试', 'danger')
            return render_template('auth/register.html', title='注册', form=form, now=datetime.now())

    return render_template('auth/register.html', title='注册', form=form, now=datetime.now())
