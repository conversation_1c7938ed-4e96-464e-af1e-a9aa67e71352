#!/usr/bin/env python3
"""
演示数据初始化脚本
为智慧食堂演示学校创建完整的演示数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import *
from sqlalchemy import text
from datetime import datetime, date, timedelta
import json

def init_demo_data():
    """初始化演示数据"""
    app = create_app()
    
    with app.app_context():
        try:
            print("开始初始化演示数据...")
            
            # 1. 获取演示学校
            demo_school = AdministrativeArea.query.filter_by(
                name='智慧食堂演示学校',
                level=3
            ).first()
            
            if not demo_school:
                print("演示学校不存在，请先运行游客登录创建演示学校")
                return
            
            print(f"找到演示学校: {demo_school.name} (ID: {demo_school.id})")
            
            # 2. 创建演示仓库
            create_demo_warehouses(demo_school)
            
            # 3. 创建演示供应商
            create_demo_suppliers(demo_school)
            
            # 4. 创建演示食材
            create_demo_ingredients()
            
            # 5. 创建演示菜单
            create_demo_menus(demo_school)
            
            # 6. 创建演示库存数据
            create_demo_stock_data(demo_school)
            
            # 7. 创建演示采购订单
            create_demo_purchase_orders(demo_school)
            
            # 8. 创建演示消耗计划
            create_demo_consumption_plans(demo_school)
            
            # 9. 创建演示员工数据
            create_demo_employees(demo_school)
            
            print("演示数据初始化完成！")
            
        except Exception as e:
            print(f"初始化演示数据时出错: {str(e)}")
            db.session.rollback()
            raise

def create_demo_warehouses(demo_school):
    """创建演示仓库"""
    print("创建演示仓库...")
    
    # 获取游客用户
    guest_user = User.query.filter_by(username='guest_demo').first()
    if not guest_user:
        print("游客用户不存在")
        return
    
    # 检查是否已存在仓库
    existing_warehouse = Warehouse.query.filter_by(area_id=demo_school.id).first()
    if existing_warehouse:
        print("演示仓库已存在，跳过创建")
        return
    
    # 创建主仓库
    sql = text('''
    INSERT INTO warehouses
    (name, area_id, location, manager_id, capacity, capacity_unit, temperature_range, humidity_range, status, notes, created_at, updated_at)
    OUTPUT inserted.id
    VALUES
    (:name, :area_id, :location, :manager_id, :capacity, :capacity_unit, :temperature_range, :humidity_range, :status, :notes, GETDATE(), GETDATE())
    ''')
    
    result = db.session.execute(sql, {
        'name': '智慧食堂中心仓库',
        'area_id': demo_school.id,
        'location': '食堂一楼东侧',
        'manager_id': guest_user.id,
        'capacity': 800.0,
        'capacity_unit': '立方米',
        'temperature_range': '-18°C ~ 25°C',
        'humidity_range': '45% ~ 65%',
        'status': '正常',
        'notes': '演示学校主要仓库，包含常温、冷藏、冷冻三个储存区域'
    })
    
    warehouse_id = result.scalar()
    print(f"创建仓库成功，ID: {warehouse_id}")
    
    # 创建储存位置
    storage_locations = [
        {'name': '常温储存区A', 'type': '常温', 'capacity': 300.0},
        {'name': '常温储存区B', 'type': '常温', 'capacity': 200.0},
        {'name': '冷藏储存区', 'type': '冷藏', 'capacity': 150.0},
        {'name': '冷冻储存区', 'type': '冷冻', 'capacity': 100.0},
        {'name': '干货储存区', 'type': '干燥', 'capacity': 50.0}
    ]
    
    for location in storage_locations:
        sql = text('''
        INSERT INTO storage_locations
        (warehouse_id, name, storage_type, capacity, capacity_unit, temperature_range, humidity_range, status, notes, created_at, updated_at)
        VALUES
        (:warehouse_id, :name, :storage_type, :capacity, :capacity_unit, :temperature_range, :humidity_range, :status, :notes, GETDATE(), GETDATE())
        ''')
        
        db.session.execute(sql, {
            'warehouse_id': warehouse_id,
            'name': location['name'],
            'storage_type': location['type'],
            'capacity': location['capacity'],
            'capacity_unit': '立方米',
            'temperature_range': get_temperature_range(location['type']),
            'humidity_range': '45% ~ 65%',
            'status': '正常',
            'notes': f'演示{location["type"]}储存区域'
        })
    
    db.session.commit()
    print("储存位置创建完成")

def get_temperature_range(storage_type):
    """根据储存类型获取温度范围"""
    ranges = {
        '常温': '15°C ~ 25°C',
        '冷藏': '0°C ~ 4°C',
        '冷冻': '-18°C ~ -15°C',
        '干燥': '18°C ~ 22°C'
    }
    return ranges.get(storage_type, '15°C ~ 25°C')

def create_demo_suppliers(demo_school):
    """创建演示供应商"""
    print("创建演示供应商...")
    
    # 检查是否已存在供应商
    existing_relation = SupplierSchoolRelation.query.filter_by(area_id=demo_school.id).first()
    if existing_relation:
        print("演示供应商已存在，跳过创建")
        return
    
    suppliers_data = [
        {
            'name': '绿色农产品有限公司',
            'contact_person': '张经理',
            'phone': '***********',
            'address': '湖南省岳阳市君山区农业园区',
            'business_license': '430600001234567',
            'category': '蔬菜水果'
        },
        {
            'name': '优质肉类供应商',
            'contact_person': '李经理',
            'phone': '***********',
            'address': '湖南省岳阳市岳阳楼区食品工业园',
            'business_license': '430600002345678',
            'category': '肉类禽蛋'
        },
        {
            'name': '新鲜海鲜配送中心',
            'contact_person': '王经理',
            'phone': '***********',
            'address': '湖南省岳阳市云溪区水产批发市场',
            'business_license': '430600003456789',
            'category': '水产海鲜'
        }
    ]
    
    for supplier_data in suppliers_data:
        # 创建供应商
        sql = text('''
        INSERT INTO suppliers
        (name, contact_person, phone, address, business_license, category, status, notes, created_at, updated_at)
        OUTPUT inserted.id
        VALUES
        (:name, :contact_person, :phone, :address, :business_license, :category, :status, :notes, GETDATE(), GETDATE())
        ''')
        
        result = db.session.execute(sql, {
            'name': supplier_data['name'],
            'contact_person': supplier_data['contact_person'],
            'phone': supplier_data['phone'],
            'address': supplier_data['address'],
            'business_license': supplier_data['business_license'],
            'category': supplier_data['category'],
            'status': 1,
            'notes': f'演示供应商 - {supplier_data["category"]}专业供应商'
        })
        
        supplier_id = result.scalar()
        
        # 创建供应商-学校关联
        sql = text('''
        INSERT INTO supplier_school_relations
        (supplier_id, area_id, contract_number, start_date, end_date, status, notes, created_at, updated_at)
        VALUES
        (:supplier_id, :area_id, :contract_number, :start_date, :end_date, :status, :notes, GETDATE(), GETDATE())
        ''')
        
        db.session.execute(sql, {
            'supplier_id': supplier_id,
            'area_id': demo_school.id,
            'contract_number': f'DEMO-{supplier_id:04d}',
            'start_date': date.today() - timedelta(days=30),
            'end_date': date.today() + timedelta(days=365),
            'status': 1,
            'notes': '演示合作协议'
        })
        
        print(f"创建供应商: {supplier_data['name']}")
    
    db.session.commit()
    print("演示供应商创建完成")

def create_demo_ingredients():
    """创建演示食材"""
    print("创建演示食材...")
    
    # 检查是否已存在食材
    existing_ingredient = Ingredient.query.filter_by(name='大白菜').first()
    if existing_ingredient:
        print("演示食材已存在，跳过创建")
        return
    
    ingredients_data = [
        # 蔬菜类
        {'name': '大白菜', 'category': '蔬菜', 'unit': '公斤', 'shelf_life': 7},
        {'name': '土豆', 'category': '蔬菜', 'unit': '公斤', 'shelf_life': 30},
        {'name': '胡萝卜', 'category': '蔬菜', 'unit': '公斤', 'shelf_life': 14},
        {'name': '西红柿', 'category': '蔬菜', 'unit': '公斤', 'shelf_life': 5},
        {'name': '黄瓜', 'category': '蔬菜', 'unit': '公斤', 'shelf_life': 3},
        
        # 肉类
        {'name': '猪肉', 'category': '肉类', 'unit': '公斤', 'shelf_life': 3},
        {'name': '牛肉', 'category': '肉类', 'unit': '公斤', 'shelf_life': 3},
        {'name': '鸡肉', 'category': '肉类', 'unit': '公斤', 'shelf_life': 2},
        {'name': '鸡蛋', 'category': '禽蛋', 'unit': '公斤', 'shelf_life': 21},
        
        # 水产
        {'name': '草鱼', 'category': '水产', 'unit': '公斤', 'shelf_life': 1},
        {'name': '带鱼', 'category': '水产', 'unit': '公斤', 'shelf_life': 1},
        
        # 主食
        {'name': '大米', 'category': '主食', 'unit': '公斤', 'shelf_life': 365},
        {'name': '面粉', 'category': '主食', 'unit': '公斤', 'shelf_life': 180},
        {'name': '食用油', 'category': '调料', 'unit': '升', 'shelf_life': 540},
        {'name': '盐', 'category': '调料', 'unit': '公斤', 'shelf_life': 1095}
    ]
    
    for ingredient_data in ingredients_data:
        sql = text('''
        INSERT INTO ingredients
        (name, category, unit, shelf_life_days, storage_requirements, status, notes, created_at, updated_at)
        VALUES
        (:name, :category, :unit, :shelf_life_days, :storage_requirements, :status, :notes, GETDATE(), GETDATE())
        ''')
        
        storage_req = get_storage_requirements(ingredient_data['category'])
        
        db.session.execute(sql, {
            'name': ingredient_data['name'],
            'category': ingredient_data['category'],
            'unit': ingredient_data['unit'],
            'shelf_life_days': ingredient_data['shelf_life'],
            'storage_requirements': storage_req,
            'status': 1,
            'notes': f'演示食材 - {ingredient_data["category"]}'
        })
    
    db.session.commit()
    print("演示食材创建完成")

def get_storage_requirements(category):
    """根据食材类别获取储存要求"""
    requirements = {
        '蔬菜': '冷藏保存，温度0-4°C',
        '肉类': '冷冻保存，温度-18°C以下',
        '禽蛋': '冷藏保存，温度0-4°C',
        '水产': '冷冻保存，温度-18°C以下',
        '主食': '常温干燥保存',
        '调料': '常温干燥保存，避光'
    }
    return requirements.get(category, '常温保存')

if __name__ == '__main__':
    init_demo_data()
