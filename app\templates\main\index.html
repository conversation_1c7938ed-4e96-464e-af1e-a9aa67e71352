<!DOCTYPE html>
<!-- saved from url=(0022)http://127.0.0.1:8080/ -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台 - 暗夜霓红首页</title>
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%);
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            letter-spacing: 0.02em;
            overflow-x: hidden;
            transition: background 0.3s, color 0.3s;
        }
        body.light-theme {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2d0036;
        }
        .neon {
            color: #ff3cac;
            text-shadow: 0 0 12px #ff3cac, 0 0 32px #784ba0;
            animation: neon-glow 2.5s infinite alternate;
        }
        .light-theme .neon {
            color: #784ba0;
            text-shadow: 0 0 12px #784ba0, 0 0 32px #ff3cac;
            animation: neon-glow-light 2.5s infinite alternate;
        }
        @keyframes neon-glow {
            0% { text-shadow: 0 0 8px #ff3cac, 0 0 16px #784ba0; }
            100% { text-shadow: 0 0 24px #ff3cac, 0 0 48px #784ba0; }
        }
        @keyframes neon-glow-light {
            0% { text-shadow: 0 0 8px #784ba0, 0 0 16px #ff3cac; }
            100% { text-shadow: 0 0 24px #784ba0, 0 0 48px #ff3cac; }
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .navbar {
            background: rgba(30,0,50,0.95);
            box-shadow: 0 2px 24px #ff3cac55;
            padding: 18px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 0 0 18px 18px;
            margin-bottom: 18px;
            transition: background 0.3s, box-shadow 0.3s;
        }
        .light-theme .navbar {
            background: rgba(255,255,255,0.95);
            box-shadow: 0 2px 24px #784ba055;
        }
        .navbar-brand {
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            letter-spacing: 2px;
            text-shadow: 0 0 12px #ff3cac, 0 0 32px #784ba0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .icon-svg {
            display: inline-flex;
            vertical-align: middle;
            margin-right: 8px;
            filter: drop-shadow(0 0 6px #ff3cac88);
        }
        .navbar-nav {
            display: flex;
            gap: 32px;
        }
        .navbar-nav a {
            color: #fff;
            font-size: 1.1rem;
            font-weight: 500;
            text-decoration: none;
            transition: color 0.2s, text-shadow 0.2s;
            text-shadow: 0 0 4px #784ba0;
        }
        .navbar-nav a:hover {
            color: #ff3cac;
            text-shadow: 0 0 12px #ff3cac;
        }
        .navbar-actions {
            display: flex;
            gap: 16px;
        }
        .btn-neon {
            background: linear-gradient(90deg, #ff3cac 0%, #784ba0 100%);
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 12px 32px;
            font-size: 1.1rem;
            font-weight: 700;
            box-shadow: 0 0 18px #ff3cac99, 0 0 4px #fff2;
            transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
            position: relative;
            overflow: hidden;
        }
        .btn-neon:after {
            content: '';
            position: absolute;
            left: -50%;
            top: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, #ff3cac55 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .btn-neon:hover {
            background: linear-gradient(90deg, #784ba0 0%, #ff3cac 100%);
            box-shadow: 0 0 32px #ff3caccc, 0 0 8px #fff4;
            transform: scale(1.04);
        }
        .btn-neon:hover:after {
            opacity: 0.3;
        }
        .hero {
            text-align: center;
            padding: 120px 0 60px 0;
            position: relative;
        }
        .hero::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 80vw;
            height: 100%;
            background: radial-gradient(circle at 50% 30%, #ff3cac33 0%, transparent 80%);
            z-index: 0;
        }
        .hero-title {
            font-size: 3.2rem;
            font-weight: 900;
            margin-bottom: 18px;
            color: #fff;
            text-shadow: 0 0 24px #ff3cac, 0 0 48px #784ba0;
            position: relative;
            z-index: 1;
        }
        .hero-desc {
            font-size: 1.3rem;
            color: #ffb6e6;
            margin-bottom: 36px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }
        .hero-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
            position: relative;
            z-index: 1;
        }
        .divider {
            width: 100px;
            height: 4px;
            margin: 32px auto 0 auto;
            background: linear-gradient(90deg, #ff3cac 0%, #784ba0 100%);
            border-radius: 2px;
            box-shadow: 0 0 12px #ff3cac99;
        }
        .features-section {
            margin: 60px 0 40px 0;
        }
        .features-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-shadow: 0 0 12px #ff3cac;
        }
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 32px;
            margin-top: 32px;
        }
        .feature-card {
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22, 0 0 0 2px #ff3cac33 inset;
            padding: 40px 28px 32px 28px;
            text-align: center;
            color: #fff;
            transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
            position: relative;
            overflow: hidden;
        }
        .light-theme .feature-card {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
            box-shadow: 0 4px 32px #784ba022, 0 0 0 2px #784ba033 inset;
            color: #2d0036;
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.04);
            box-shadow: 0 8px 48px #ff3cac77, 0 0 0 2px #ff3cac99 inset;
        }
        .light-theme .feature-card:hover {
            box-shadow: 0 8px 48px #784ba077, 0 0 0 2px #784ba099 inset;
        }
        .feature-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 18px;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }
        .feature-desc {
            color: #ffb6e6;
            font-size: 1rem;
        }
        .light-theme .feature-desc {
            color: #784ba0;
        }
        .contact-section {
            margin: 60px 0 0 0;
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22;
            padding: 48px 24px;
            color: #fff;
            position: relative;
            transition: background 0.3s;
        }
        .light-theme .contact-section {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
        }
        .contact-title {
            font-size: 2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-align: center;
            text-shadow: 0 0 12px #ff3cac;
        }
        .contact-info {
            text-align: center;
            color: #ffb6e6;
            font-size: 1.1rem;
            margin-bottom: 18px;
        }
        .light-theme .contact-info {
            color: #784ba0;
        }
        .contact-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
        }
        .footer {
            text-align: center;
            color: #ffb6e6;
            padding: 32px 0 12px 0;
            font-size: 1rem;
            margin-top: 60px;
            letter-spacing: 1px;
        }
        .light-theme .footer {
            color: #784ba0;
        }
        /* 粒子背景装饰 */
        .particles {
            position: fixed;
            left: 0; top: 0; width: 100vw; height: 100vh;
            pointer-events: none;
            z-index: 0;
        }
        /* 视频教学区域样式 */
        .tutorial-section {
            margin: 60px 0;
            padding: 40px 0;
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22;
            transition: background 0.3s;
        }
        .light-theme .tutorial-section {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
        }
        .tutorial-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 32px;
            text-shadow: 0 0 12px #ff3cac;
        }
        .tutorial-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        .tutorial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            opacity: 0;
            transform: translateX(20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        .tutorial-grid.active {
            opacity: 1;
            transform: translateX(0);
        }
        .tutorial-card {
            background: rgba(45, 0, 54, 0.6);
            border-radius: 16px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s, background 0.3s;
            cursor: pointer;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .light-theme .tutorial-card {
            background: rgba(255, 255, 255, 0.6);
        }
        .tutorial-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 32px #ff3cac55;
        }
        .tutorial-thumbnail {
            width: 100%;
            height: 200px;
            background: #1a0025;
            position: relative;
            overflow: hidden;
        }
        .tutorial-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }
        .tutorial-card:hover .tutorial-thumbnail img {
            transform: scale(1.1);
        }
        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 48px;
            height: 48px;
            background: rgba(255, 60, 172, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 24px #ff3cac;
            transition: transform 0.3s, background-color 0.3s;
        }
        .tutorial-card:hover .play-icon {
            transform: translate(-50%, -50%) scale(1.1);
            background: rgba(255, 60, 172, 1);
        }
        .tutorial-info {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .tutorial-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #fff;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .tutorial-desc {
            font-size: 0.95rem;
            color: #ffb6e6;
            line-height: 1.5;
            flex-grow: 1;
        }
        .light-theme .tutorial-desc {
            color: #784ba0;
        }
        .tutorial-duration {
            font-size: 0.85rem;
            color: #ff3cac;
            margin-top: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .tutorial-duration svg {
            width: 16px;
            height: 16px;
        }
        .tutorial-nav {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
            margin-top: 32px;
        }
        .tutorial-nav-btn {
            background: rgba(255, 60, 172, 0.2);
            border: 2px solid #ff3cac;
            color: #ff3cac;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tutorial-nav-btn:hover {
            background: rgba(255, 60, 172, 0.4);
            transform: scale(1.1);
        }
        .tutorial-nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .tutorial-nav-dots {
            display: flex;
            gap: 8px;
        }
        .tutorial-nav-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 60, 172, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }
        .tutorial-nav-dot.active {
            background: #ff3cac;
            transform: scale(1.2);
        }
        @media (max-width: 1200px) {
            .tutorial-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 768px) {
            .tutorial-grid {
                grid-template-columns: 1fr;
            }
            .tutorial-thumbnail {
                height: 180px;
            }
        }
        /* 视频模态框样式 */
        .video-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .video-modal.active {
            display: flex;
            opacity: 1;
        }
        .video-container {
            position: relative;
            width: 90%;
            max-width: 1000px;
            margin: auto;
            background: #1a0025;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 0 48px #ff3cac55;
        }
        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
        }
        .close-modal {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 60, 172, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 0 24px #ff3cac;
            transition: transform 0.2s;
        }
        .close-modal:hover {
            transform: scale(1.1);
        }
        /* 简化的主题切换按钮 */
        .theme-toggle.simple {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            position: relative;
            margin-left: 12px;
        }

        .theme-toggle.simple:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .theme-toggle.simple i {
            font-size: 16px;
        }
        /* 简化的主题下拉菜单 */
        .theme-dropdown.simple {
            position: absolute;
            top: 100%;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 8px;
            margin-top: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            display: none;
            z-index: 1000;
            min-width: 200px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        .theme-dropdown::after {
            content: '';
            position: absolute;
            top: -10px;
            left: 30px;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, rgba(45, 0, 54, 0.95), rgba(75, 0, 130, 0.95));
            transform: rotate(45deg);
            border-radius: 4px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .theme-dropdown.simple.active {
            display: block;
            animation: simpleDropdownFade 0.2s ease-out;
        }

        @keyframes simpleDropdownFade {
            from { 
                opacity: 0; 
                transform: translateY(-10px);
            }
            to { 
                opacity: 1; 
                transform: translateY(0);
            }
        }
        /* 简化的主题选项 */
        .theme-dropdown.simple .theme-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s ease;
            margin: 2px 0;
        }
        .theme-dropdown.simple .theme-option:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .theme-dropdown.simple .theme-option.active {
            background-color: rgba(255, 60, 172, 0.1);
            color: #ff3cac;
        }
        .theme-option.active {
            background: linear-gradient(135deg, rgba(255, 60, 172, 0.4), rgba(120, 75, 160, 0.4));
            transform: translateX(15px) scale(1.05);
            box-shadow:
                0 15px 40px rgba(255, 60, 172, 0.5),
                0 0 0 2px rgba(255, 60, 172, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 60, 172, 0.6);
            animation: activeGlow 2s ease-in-out infinite alternate;
        }

        @keyframes activeGlow {
            0% {
                box-shadow:
                    0 15px 40px rgba(255, 60, 172, 0.5),
                    0 0 0 2px rgba(255, 60, 172, 0.6),
                    inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
            100% {
                box-shadow:
                    0 20px 50px rgba(255, 60, 172, 0.7),
                    0 0 0 3px rgba(255, 60, 172, 0.8),
                    inset 0 1px 0 rgba(255, 255, 255, 0.5);
            }
        }
        /* 简化的主题预览球 */
        .theme-dropdown.simple .theme-preview {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }
        /* 简化的主题名称 */
        .theme-dropdown.simple .theme-name {
            font-size: 14px;
            font-weight: 500;
            flex: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .theme-toggle.simple {
                padding: 6px;
                margin-left: 8px;
            }

            .theme-dropdown.simple {
                min-width: 160px;
                right: -10px;
            }

            .theme-dropdown.simple .theme-option {
                padding: 6px 10px;
            }

            .theme-dropdown.simple .theme-preview {
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }

            .theme-dropdown.simple .theme-name {
                font-size: 13px;
            }
        }

        .theme-option .theme-favorite {
            margin-left: auto;
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            color: #ffc107;
            font-size: 1.1rem;
            padding: 8px;
            border-radius: 50%;
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .theme-option:hover .theme-favorite {
            opacity: 1;
            transform: scale(1.2) rotate(15deg);
            background: rgba(255, 193, 7, 0.2);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
        }

        .theme-option.favorited .theme-favorite {
            opacity: 1;
            color: #ffc107 !important;
            background: rgba(255, 193, 7, 0.3);
            animation: starSpectacular 3s ease-in-out infinite;
        }

        @keyframes starSpectacular {
            0%, 100% {
                transform: scale(1) rotate(0deg);
                box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
            }
            25% {
                transform: scale(1.1) rotate(5deg);
                box-shadow: 0 6px 20px rgba(255, 193, 7, 0.6);
            }
            50% {
                transform: scale(1.2) rotate(-5deg);
                box-shadow: 0 8px 25px rgba(255, 193, 7, 0.8);
            }
            75% {
                transform: scale(1.1) rotate(5deg);
                box-shadow: 0 6px 20px rgba(255, 193, 7, 0.6);
            }
        }
        /* 主题样式 - DeepSeek风格增强 */
        body.theme-dark {
            background: linear-gradient(135deg,
                #8B5CF6 0%,
                #F43F5E 25%,
                #06B6D4 50%,
                #8B5CF6 75%,
                #F43F5E 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            color: #fff;
            position: relative;
        }

        body.theme-light {
            background: linear-gradient(135deg,
                #F97316 0%,
                #EF4444 25%,
                #22C55E 50%,
                #3B82F6 75%,
                #F97316 100%);
            background-size: 400% 400%;
            animation: gradientShift 12s ease infinite;
            color: #1f2937;
            position: relative;
        }

        body.theme-ocean {
            background: linear-gradient(135deg,
                #2563EB 0%,
                #8B5CF6 25%,
                #06B6D4 50%,
                #0EA5E9 75%,
                #2563EB 100%);
            background-size: 400% 400%;
            animation: gradientShift 18s ease infinite;
            color: #fff;
            position: relative;
        }

        body.theme-forest {
            background: linear-gradient(135deg,
                #10B981 0%,
                #0EA5E9 25%,
                #F59E0B 50%,
                #22C55E 75%,
                #10B981 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            color: #fff;
            position: relative;
        }

        body.theme-sunset {
            background: linear-gradient(135deg,
                #6D28D9 0%,
                #EC4899 25%,
                #F59E0B 50%,
                #EF4444 75%,
                #6D28D9 100%);
            background-size: 400% 400%;
            animation: gradientShift 14s ease infinite;
            color: #fff;
            position: relative;
        }

        /* 动态渐变动画 */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            25% { background-position: 100% 50%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 50% 100%; }
            100% { background-position: 0% 50%; }
        }

        /* 为每个主题添加动态粒子效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: particleFloat 25s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes particleFloat {
            0%, 100% {
                background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            }
            25% {
                background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 60% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            }
            50% {
                background: radial-gradient(circle at 60% 60%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            }
            75% {
                background: radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 60% 60%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            }
        }
        /* 主题预览颜色 - 深海科技蓝风格 */
        .theme-preview.dark {
            background: linear-gradient(135deg, #8B5CF6 0%, #F43F5E 50%, #06B6D4 100%);
            position: relative;
        }

        .theme-preview.light {
            background: linear-gradient(135deg, #F97316 0%, #EF4444 50%, #22C55E 100%);
            position: relative;
        }

        .theme-preview.ocean {
            background: linear-gradient(135deg, #2563EB 0%, #8B5CF6 50%, #06B6D4 100%);
            position: relative;
        }

        .theme-preview.forest {
            background: linear-gradient(135deg, #10B981 0%, #0EA5E9 50%, #F59E0B 100%);
            position: relative;
        }

        .theme-preview.sunset {
            background: linear-gradient(135deg, #6D28D9 0%, #EC4899 50%, #F59E0B 100%);
            position: relative;
        }

        /* 为每个主题预览添加动态光效 */
        .theme-preview.dark::after,
        .theme-preview.light::after,
        .theme-preview.ocean::after,
        .theme-preview.forest::after,
        .theme-preview.sunset::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(255, 255, 255, 0.3) 0%,
                transparent 50%,
                rgba(255, 255, 255, 0.3) 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
            border-radius: 50%;
        }

        .theme-option:hover .theme-preview.dark::after,
        .theme-option:hover .theme-preview.light::after,
        .theme-option:hover .theme-preview.ocean::after,
        .theme-option:hover .theme-preview.forest::after,
        .theme-option:hover .theme-preview.sunset::after {
            opacity: 1;
            animation: lightSweep 1.5s ease-in-out infinite;
        }

        @keyframes lightSweep {
            0% { transform: translateX(-100%) rotate(45deg); }
            100% { transform: translateX(100%) rotate(45deg); }
        }

        /* 主题控制面板样式 - DeepSeek风格 */
        .theme-controls-section {
            border-top: 2px solid rgba(255, 255, 255, 0.2);
            margin: 20px 0 0 0;
            padding-top: 20px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
        }

        .theme-controls-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(255, 60, 172, 0.1) 0%,
                transparent 50%,
                rgba(120, 75, 160, 0.1) 100%);
            animation: controlsShimmer 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes controlsShimmer {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        .controls-header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .controls-title {
            font-size: 1rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .controls-title i {
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .controls-buttons {
            display: flex;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .control-btn {
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .control-btn i {
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .control-btn span {
            font-size: 0.8rem;
            letter-spacing: 0.5px;
        }

        .btn-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
            border-radius: 50%;
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }

        .control-btn:hover {
            transform: translateY(-3px) scale(1.05);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow:
                0 10px 25px rgba(255, 60, 172, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .control-btn:hover .btn-glow {
            width: 100%;
            height: 100%;
        }

        .control-btn:hover i {
            transform: scale(1.2);
        }

        .control-btn:active {
            transform: translateY(-1px) scale(1.02);
        }

        .control-btn.active {
            background: linear-gradient(135deg, rgba(255, 60, 172, 0.4), rgba(120, 75, 160, 0.4));
            border-color: rgba(255, 60, 172, 0.6);
            box-shadow:
                0 8px 20px rgba(255, 60, 172, 0.4),
                0 0 0 2px rgba(255, 60, 172, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            animation: btnActivePulse 2s ease-in-out infinite;
        }

        @keyframes btnActivePulse {
            0%, 100% {
                box-shadow:
                    0 8px 20px rgba(255, 60, 172, 0.4),
                    0 0 0 2px rgba(255, 60, 172, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
            50% {
                box-shadow:
                    0 12px 30px rgba(255, 60, 172, 0.6),
                    0 0 0 3px rgba(255, 60, 172, 0.5),
                    inset 0 1px 0 rgba(255, 255, 255, 0.5);
            }
        }

        .preview-btn:hover {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(59, 130, 246, 0.3));
            border-color: rgba(6, 182, 212, 0.5);
        }

        .auto-btn:hover {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(34, 197, 94, 0.3));
            border-color: rgba(16, 185, 129, 0.5);
        }

        /* 主题选择成功动画 */
        @keyframes themeSelectSuccess {
            0% {
                transform: translateX(15px) scale(1.05);
            }
            25% {
                transform: translateX(20px) scale(1.1);
                box-shadow:
                    0 20px 50px rgba(255, 60, 172, 0.8),
                    0 0 0 4px rgba(255, 60, 172, 0.8),
                    inset 0 1px 0 rgba(255, 255, 255, 0.6);
            }
            50% {
                transform: translateX(25px) scale(1.15);
                box-shadow:
                    0 25px 60px rgba(255, 60, 172, 1),
                    0 0 0 6px rgba(255, 60, 172, 1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            75% {
                transform: translateX(20px) scale(1.1);
                box-shadow:
                    0 20px 50px rgba(255, 60, 172, 0.8),
                    0 0 0 4px rgba(255, 60, 172, 0.8),
                    inset 0 1px 0 rgba(255, 255, 255, 0.6);
            }
            100% {
                transform: translateX(15px) scale(1.05);
                box-shadow:
                    0 15px 40px rgba(255, 60, 172, 0.5),
                    0 0 0 2px rgba(255, 60, 172, 0.6),
                    inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
        }

        /* 页面切换过渡效果增强 */
        body {
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .theme-dropdown {
                min-width: 300px;
                left: -50px;
                padding: 16px;
            }

            .theme-option {
                padding: 14px 16px;
                margin: 6px 0;
            }

            .theme-preview {
                width: 32px;
                height: 32px;
                margin-right: 16px;
            }

            .theme-name {
                font-size: 1rem;
            }

            .theme-controls-section {
                padding: 16px;
                margin: 16px 0 0 0;
            }

            .control-btn {
                padding: 10px 12px;
                font-size: 0.85rem;
            }

            .control-btn i {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .theme-dropdown {
                min-width: 280px;
                left: -80px;
            }

            .controls-buttons {
                flex-direction: column;
                gap: 8px;
            }

            .control-btn {
                flex-direction: row;
                justify-content: center;
                gap: 8px;
            }
        }
    </style>
</head>
<body class="theme-dark">
    <canvas class="particles" width="1745" height="828"></canvas>
    <div class="container">
        <nav class="navbar">
            <div class="navbar-brand">
                <span class="icon-svg">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 21h18M8.5 17v-7.5a2.5 2.5 0 1 1 5 0V17M12 17v-7.5"></path><circle cx="12" cy="5" r="2.5"></circle></svg>
                </span>
                智慧食堂平台
                </div>
            <div class="navbar-nav">
                <a href="http://127.0.0.1:8080/#features">核心功能</a>
                <a href="http://127.0.0.1:8080/#tutorials">视频教学</a>
                <a href="http://127.0.0.1:8080/#contact">联系我们</a>
            </div>
            <div class="navbar-actions">
                <a href="http://127.0.0.1:8080/login" class="btn-neon">登录</a>
                <a href="http://127.0.0.1:8080/guest-login" class="btn-neon guest-btn" title="无需注册，直接体验系统功能">
                    <i class="fas fa-user-secret"></i> 游客体验
                </a>
                <a href="http://127.0.0.1:8080/register" class="btn-neon" style="background:linear-gradient(90deg,#ff3cac 0%,#784ba0 100%);color:#fff;">免费注册</a>

                <!-- 简化的主题切换按钮 - 移到最右边 -->
                <div class="theme-toggle simple" id="themeToggle" title="切换主题">
                    <i class="fas fa-palette"></i>
                    <div class="theme-dropdown simple" id="themeDropdown">
                        <div class="theme-option active" data-theme="dark">
                <div class="theme-preview dark"></div>
                            <span class="theme-name">暗夜霓虹</span>
            </div>
            <div class="theme-option" data-theme="light">
                <div class="theme-preview light"></div>
                            <span class="theme-name">清新明亮</span>
            </div>
            <div class="theme-option" data-theme="ocean">
                <div class="theme-preview ocean"></div>
                            <span class="theme-name">深海蓝调</span>
            </div>
            <div class="theme-option" data-theme="forest">
                <div class="theme-preview forest"></div>
                            <span class="theme-name">森林绿意</span>
            </div>
            <div class="theme-option" data-theme="sunset">
                <div class="theme-preview sunset"></div>
                            <span class="theme-name">日落紫霞</span>
            </div>
        </div>
    </div>
                    </div>
        </nav>
        <section class="hero">
            <div class="hero-title neon">智慧食堂管理平台</div>
            <div class="hero-desc">打造智能化校园食堂管理新生态，让食品安全更透明，让管理更高效，让服务更贴心</div>
            <div class="hero-actions">
                <a href="http://127.0.0.1:8080/guest-login" class="btn-neon guest-btn" style="background:linear-gradient(90deg,#10B981 0%,#059669 100%);color:#fff;" title="无需注册，直接体验系统功能">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></span>游客体验</a>
                <a href="http://127.0.0.1:8080/register" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="7" r="4"></circle><path d="M5.5 21a7.5 7.5 0 0 1 13 0"></path></svg></span>免费注册</a>
                <a href="http://127.0.0.1:8080/#features" class="btn-neon" style="background:linear-gradient(90deg,#784ba0 0%,#ff3cac 100%);color:#fff;">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><circle cx="12" cy="8" r="1"></circle></svg></span>了解更多</a>
                </div>
            <div class="divider"></div>
    </section>
        <section class="features-section" id="features">
            <div class="features-title">核心功能</div>
            <div class="features-list">
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2l2.09 6.26L20 9.27l-5 4.87L16.18 22 12 18.56 7.82 22 9 14.14l-5-4.87 5.91-.91z"></path></svg></div>
                    <div class="feature-title">食品安全管理</div>
                    <div class="feature-desc">全程追溯，确保食品安全。从食材采购到餐后服务，全方位保障食品安全。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg></div>
                    <div class="feature-title">智能采购系统</div>
                    <div class="feature-desc">智能价格对比，自动生成采购单，简化采购流程，提升采购效率。</div>
                        </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2"></rect><path d="M3 9h18M9 21V9"></path></svg></div>
                    <div class="feature-title">出入库管理</div>
                    <div class="feature-desc">完整管理出入库流程，自动生成台账报表，实时监控库存情况。</div>
                            </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="16" rx="2"></rect><path d="M3 10h18"></path></svg></div>
                    <div class="feature-title">灵活菜单管理</div>
                    <div class="feature-desc">自定义菜单，营养搭配推荐，满足不同需求。</div>
                        </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="7" r="4"></circle><path d="M5.5 21a7.5 7.5 0 0 1 13 0"></path></svg></div>
                    <div class="feature-title">师生用餐管理</div>
                    <div class="feature-desc">刷卡、扫码、预约多种用餐方式，数据实时统计。</div>
                    </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3v18h18"></path><path d="M18 21V3"></path><path d="M3 9h15"></path></svg></div>
                    <div class="feature-title">财务报表分析</div>
                    <div class="feature-desc">自动生成多维度财务报表，辅助决策。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 8v4l3 3"></path></svg></div>
                    <div class="feature-title">系统权限管理</div>
                    <div class="feature-desc">多级权限分配，安全高效。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 15a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4H7a4 4 0 0 0-4 4z"></path><path d="M7 7h10v4H7z"></path></svg></div>
                    <div class="feature-title">云端数据备份</div>
                    <div class="feature-desc">数据云端存储，安全可靠，随时恢复。</div>
            </div>
        </div>
    </section>

        <!-- 游客体验展示区域 -->
        <section class="guest-experience-section">
        <div class="container">
                <div class="guest-experience-card">
                    <div class="guest-experience-header">
                        <div class="guest-icon">
                            <i class="fas fa-user-secret"></i>
                </div>
                        <h3>🎯 游客体验模式</h3>
                        <p>无需注册，立即体验完整的智慧食堂管理功能</p>
            </div>

                    <div class="guest-features">
                        <div class="guest-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="feature-content">
                                <h4>完整功能预览</h4>
                                <p>体验食堂管理的所有核心功能模块</p>
                    </div>
                </div>

                        <div class="guest-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="feature-content">
                                <h4>安全无风险</h4>
                                <p>演示环境，不会影响真实数据</p>
                    </div>
                </div>

                        <div class="guest-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="feature-content">
                                <h4>即时访问</h4>
                                <p>点击即可进入，无需等待审核</p>
                    </div>
                </div>

                        <div class="guest-feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="feature-content">
                                <h4>全平台支持</h4>
                                <p>支持电脑、平板、手机多端体验</p>
                        </div>
                    </div>
                </div>

                    <div class="guest-experience-footer">
                        <a href="http://127.0.0.1:8080/guest-login" class="guest-cta-btn">
                            <i class="fas fa-rocket"></i>
                            立即开始体验
                        </a>
                        <p class="guest-note">
                            <i class="fas fa-info-circle"></i>
                            体验完成后，您可以选择注册正式账户
                        </p>
                </div>
            </div>
        </div>
    </section>

        <!-- 视频教学区域 -->
        <section class="tutorial-section" id="tutorials">
            <div class="tutorial-title">视频教学</div>
            <div class="tutorial-container">
                <div class="tutorial-grid active" id="videoGrid"><div class="tutorial-card" data-video="/static/videos/consumption_plan/20250606190209_42db9fea_bandicam_2025-06-05_22-34-44-050.mp4">
                <div class="tutorial-thumbnail">
                    <img src="./indexku_files/thumb_20250606190209_d59f7482_123.png" alt="消耗量计划">
                    <div class="play-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
            </div>
                        </div>
                <div class="tutorial-info">
                                <div>
                        <div class="tutorial-name">消耗量计划</div>
                        <div class="tutorial-desc">消耗量计划</div>
                                </div>
                    <div class="tutorial-duration">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        未知
                            </div>
                        </div>
            </div><div class="tutorial-card" data-video="/static/videos/daily_management/20250606192643_c5579bb3_bandicam_2025-06-05_22-34-44-050.mp4">
                <div class="tutorial-thumbnail">
                    <img src="./indexku_files/thumb_20250606192643_6a43eedc_123.png" alt="日常工作检查">
                    <div class="play-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                    </div>
                </div>
                <div class="tutorial-info">
                                <div>
                        <div class="tutorial-name">日常工作检查</div>
                        <div class="tutorial-desc">日常工作检查</div>
                                </div>
                    <div class="tutorial-duration">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        未知
                            </div>
                        </div>
            </div><div class="tutorial-card" data-video="/static/videos/daily_management/20250605020132_5b5c09cc_bandicam_2025-06-01_20-46-48-444.mp4">
                <div class="tutorial-thumbnail">
                    <img src="./indexku_files/thumb_20250605020132_076d20ee_123.png" alt="检查记录">
                    <div class="play-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                    </div>
                </div>
                <div class="tutorial-info">
                                    <div>
                        <div class="tutorial-name">检查记录</div>
                        <div class="tutorial-desc">检查记录</div>
                                    </div>
                    <div class="tutorial-duration">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        未知
                                </div>
                            </div>
            </div></div>
                <div class="tutorial-nav">
                    <button class="tutorial-nav-btn" id="prevBtn" disabled="">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </button>
                    <div class="tutorial-nav-dots" id="navDots"><div class="tutorial-nav-dot active"></div><div class="tutorial-nav-dot "></div><div class="tutorial-nav-dot "></div><div class="tutorial-nav-dot "></div><div class="tutorial-nav-dot "></div></div>
                    <button class="tutorial-nav-btn" id="nextBtn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                                        </button>
            </div>
        </div>
    </section>

        <!-- 视频播放模态框 -->
        <div class="video-modal" id="videoModal">
            <div class="video-container">
                <video class="video-player" controls="">
                    <source src="" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
                <div class="close-modal">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                    </div>
                </div>
                </div>

        <section class="contact-section" id="contact">
            <div class="contact-title">联系我们</div>
            <div class="contact-info">专业的技术团队为您提供7×24小时服务支持<br>电话：18373062333 &nbsp;|&nbsp; 邮箱：<EMAIL> &nbsp;|&nbsp; 湖南·岳阳</div>
            <div class="contact-actions">
                <a href="mailto:<EMAIL>" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2"></rect><path d="M22 6l-10 7L2 6"></path></svg></span>邮件咨询</a>
                <a href="tel:18373062333" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92V19a2 2 0 0 1-2.18 2A19.72 19.72 0 0 1 3 5.18 2 2 0 0 1 5 3h2.09a2 2 0 0 1 2 1.72c.13 1.13.37 2.23.72 3.28a2 2 0 0 1-.45 2.11l-1.27 1.27a16 16 0 0 0 6.29 6.29l1.27-1.27a2 2 0 0 1 2.11-.45c1.05.35 2.15.59 3.28.72A2 2 0 0 1 22 16.92z"></path></svg></span>电话咨询</a>
                </div>
        </section>
        <div class="footer">
            <div>© 2025 智慧食堂平台. All rights reserved.</div>
            <div>专注校园食堂智能化管理解决方案</div>
                    </div>
                </div>
    <script>
    // 首页主题切换功能 - 与系统主题切换器兼容
    const themeToggle = document.getElementById('themeToggle');
    const themeDropdown = document.getElementById('themeDropdown');
    const body = document.body;
    const themeOptions = document.querySelectorAll('.theme-option');

    // 检查本地存储中的主题设置（与系统保持一致）
    const savedTheme = localStorage.getItem('user-theme') || localStorage.getItem('theme') || 'dark';
    body.className = `theme-${savedTheme}`;
    updateActiveTheme(savedTheme);

    // 主题切换事件
    themeToggle.addEventListener('click', (e) => {
        e.stopPropagation();
        themeDropdown.classList.toggle('active');
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', (e) => {
        if (!themeToggle.contains(e.target)) {
            themeDropdown.classList.remove('active');
        }
    });

    // 简化的主题选项点击事件
    themeOptions.forEach(option => {
        option.addEventListener('click', (e) => {
            // 简化的主题切换
            const theme = option.dataset.theme;

            // 直接切换主题
            body.className = `theme-${theme}`;
            updateActiveTheme(theme);
            localStorage.setItem('user-theme', theme);
            localStorage.setItem('theme', theme); // 兼容旧版本
            themeDropdown.classList.remove('active');

            // 显示简单提示
            showIndexNotification(`已切换到 ${getThemeName(theme)} 主题`, 'success');
                });
            });

    // 移除了复杂的预览和自动切换功能，保持简洁

    // 更新活动主题样式
    function updateActiveTheme(theme) {
        themeOptions.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === theme);
        });
    }

    // 移除了收藏功能，保持简洁

    // 获取主题名称
    function getThemeName(theme) {
        const themeNames = {
            'dark': '暗夜霓虹',
            'light': '清新明亮',
            'ocean': '深海蓝调',
            'forest': '森林绿意',
            'sunset': '日落紫霞'
        };
        return themeNames[theme] || theme;
    }

    // 显示通知 - DeepSeek风格增强
    function showIndexNotification(message, type = 'info') {
        const notification = document.createElement('div');

        // 根据类型设置不同的样式
        let bgGradient, iconClass, borderColor;
        switch(type) {
            case 'success':
                bgGradient = 'linear-gradient(135deg, #10B981 0%, #059669 100%)';
                iconClass = 'fas fa-check-circle';
                borderColor = 'rgba(16, 185, 129, 0.5)';
                break;
            case 'warning':
                bgGradient = 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)';
                iconClass = 'fas fa-exclamation-triangle';
                borderColor = 'rgba(245, 158, 11, 0.5)';
                break;
            case 'error':
                bgGradient = 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)';
                iconClass = 'fas fa-times-circle';
                borderColor = 'rgba(239, 68, 68, 0.5)';
                break;
            default:
                bgGradient = 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)';
                iconClass = 'fas fa-info-circle';
                borderColor = 'rgba(139, 92, 246, 0.5)';
        }

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px;">
                <i class="${iconClass}" style="font-size: 1.2rem; animation: notificationIcon 0.6s ease-out;"></i>
                <span style="flex: 1; font-weight: 500;">${message}</span>
                <button onclick="this.closest('div').remove()" style="background: none; border: none; color: rgba(255,255,255,0.7); cursor: pointer; font-size: 1.1rem; padding: 4px;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgGradient};
            color: white;
            padding: 16px 20px;
            border-radius: 15px;
            z-index: 9999;
            animation: notificationSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.3),
                0 0 0 1px ${borderColor},
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            max-width: 350px;
            min-width: 280px;
            font-size: 0.9rem;
            border: 1px solid ${borderColor};
        `;

        // 添加动画样式
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes notificationSlideIn {
                    0% {
                        opacity: 0;
                        transform: translateX(100%) scale(0.8);
                        filter: blur(10px);
                    }
                    50% {
                        opacity: 0.8;
                        transform: translateX(-10px) scale(1.05);
                        filter: blur(2px);
                    }
                    100% {
                        opacity: 1;
                        transform: translateX(0) scale(1);
                        filter: blur(0);
                    }
                }

                @keyframes notificationSlideOut {
                    0% {
                        opacity: 1;
                        transform: translateX(0) scale(1);
                    }
                    100% {
                        opacity: 0;
                        transform: translateX(100%) scale(0.8);
                        filter: blur(5px);
                    }
                }

                @keyframes notificationIcon {
                    0% { transform: scale(0) rotate(-180deg); }
                    50% { transform: scale(1.2) rotate(-90deg); }
                    100% { transform: scale(1) rotate(0deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // 自动消失
        setTimeout(() => {
            notification.style.animation = 'notificationSlideOut 0.4s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 400);
        }, 4000);

        // 添加悬停暂停功能
        notification.addEventListener('mouseenter', () => {
            notification.style.animationPlayState = 'paused';
        });

        notification.addEventListener('mouseleave', () => {
            notification.style.animationPlayState = 'running';
        });
    }

    // 简化版本，无需初始化收藏状态

    // 粒子背景动画
    const canvas = document.querySelector('.particles');
    const ctx = canvas.getContext('2d');
    let w = window.innerWidth, h = window.innerHeight;
    let particles = [];
    function resize() {
        w = window.innerWidth; h = window.innerHeight;
        canvas.width = w; canvas.height = h;
    }
    window.addEventListener('resize', resize);
    resize();
    function randomColor() {
        const colors = ['#ff3cac', '#784ba0', '#fff', '#ffb6e6'];
        return colors[Math.floor(Math.random()*colors.length)];
    }
    function createParticle() {
        return {
            x: Math.random()*w,
            y: Math.random()*h,
            r: Math.random()*2+1,
            dx: (Math.random()-0.5)*0.5,
            dy: (Math.random()-0.5)*0.5,
            color: randomColor(),
            alpha: Math.random()*0.5+0.5
        };
    }
    for(let i=0;i<60;i++) particles.push(createParticle());
    function draw() {
        ctx.clearRect(0,0,w,h);
        for(const p of particles) {
            ctx.save();
            ctx.globalAlpha = p.alpha;
            ctx.beginPath();
            ctx.arc(p.x,p.y,p.r,0,2*Math.PI);
            ctx.fillStyle = p.color;
            ctx.shadowColor = p.color;
            ctx.shadowBlur = 12;
            ctx.fill();
            ctx.restore();
            p.x += p.dx; p.y += p.dy;
            if(p.x<0||p.x>w) p.x = Math.random()*w;
            if(p.y<0||p.y>h) p.y = Math.random()*h;
        }
        requestAnimationFrame(draw);
    }
    draw();

    // 加载视频数据
    let currentPage = 0;
    let videosPerPage = 3;
    let allVideos = [];

    function loadVideos() {
        fetch('/api/guide/videos/all')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.videos) {
                    allVideos = data.videos;
                    updateVideoDisplay();
                    updateNavigation();
                }
            })
            .catch(error => {
                console.error('加载视频失败:', error);
                const videoGrid = document.getElementById('videoGrid');
                videoGrid.innerHTML = '<div class="error-message">加载视频失败，请稍后重试</div>';
            });
    }

    function updateVideoDisplay() {
        const videoGrid = document.getElementById('videoGrid');
        videoGrid.innerHTML = ''; // 清空现有内容

        const startIndex = currentPage * videosPerPage;
        const endIndex = Math.min(startIndex + videosPerPage, allVideos.length);
        const currentVideos = allVideos.slice(startIndex, endIndex);

        currentVideos.forEach(video => {
            const videoCard = document.createElement('div');
            videoCard.className = 'tutorial-card';
            videoCard.dataset.video = video.url;

            videoCard.innerHTML = `
                <div class="tutorial-thumbnail">
                    <img src="${video.thumbnail}" alt="${video.name}">
                    <div class="play-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                    </div>
                </div>
                <div class="tutorial-info">
                    <div>
                        <div class="tutorial-name">${video.name}</div>
                        <div class="tutorial-desc">${video.description || '暂无描述'}</div>
                    </div>
                    <div class="tutorial-duration">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        ${video.duration || '未知时长'}
                    </div>
                </div>
            `;

            videoGrid.appendChild(videoCard);
        });

        // 重新绑定视频点击事件
        bindVideoEvents();
    }

    function updateNavigation() {
        const totalPages = Math.ceil(allVideos.length / videosPerPage);
        const navDots = document.getElementById('navDots');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        // 更新导航点
        navDots.innerHTML = '';
        for (let i = 0; i < totalPages; i++) {
            const dot = document.createElement('div');
            dot.className = `tutorial-nav-dot ${i === currentPage ? 'active' : ''}`;
            dot.addEventListener('click', () => {
                currentPage = i;
                updateVideoDisplay();
                updateNavigation();
            });
            navDots.appendChild(dot);
        }

        // 更新按钮状态
        prevBtn.disabled = currentPage === 0;
        nextBtn.disabled = currentPage === totalPages - 1;
    }

    // 绑定导航按钮事件
    document.getElementById('prevBtn').addEventListener('click', () => {
        if (currentPage > 0) {
            currentPage--;
            updateVideoDisplay();
            updateNavigation();
        }
    });

    document.getElementById('nextBtn').addEventListener('click', () => {
        const totalPages = Math.ceil(allVideos.length / videosPerPage);
        if (currentPage < totalPages - 1) {
            currentPage++;
            updateVideoDisplay();
            updateNavigation();
        }
    });

    // 绑定视频点击事件
    function bindVideoEvents() {
        const tutorialCards = document.querySelectorAll('.tutorial-card');
        const videoModal = document.getElementById('videoModal');
        const videoPlayer = videoModal.querySelector('video');
        const closeModal = videoModal.querySelector('.close-modal');

        tutorialCards.forEach(card => {
            card.addEventListener('click', () => {
                const videoSrc = card.dataset.video;
                videoPlayer.src = videoSrc;
                videoModal.classList.add('active');
                videoPlayer.play();
            });
        });

        closeModal.addEventListener('click', () => {
            videoModal.classList.remove('active');
            videoPlayer.pause();
            videoPlayer.src = '';
        });

        videoModal.addEventListener('click', (e) => {
            if (e.target === videoModal) {
                videoModal.classList.remove('active');
                videoPlayer.pause();
                videoPlayer.src = '';
            }
        });
    }

    // 页面加载完成后加载视频
    document.addEventListener('DOMContentLoaded', loadVideos);
    </script>

</body><div class="xl-chrome-ext-bar_4DB361DE-01F7-4376-B494-639E489D19ED" id="xl_chrome_ext_4DB361DE-01F7-4376-B494-639E489D19ED" data-v-app="" style="display: block;"><div class=""><div id="xl_chrome_ext_video_tag_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED" config="[object Object]" uiversion="v2" exception="false" latestvideosrc="" isshowvideotag="false" isshowdownloadbar="true" isshowcloudaddbar="false"><div class="_video_op_wrapper_96mx8_1" config="[object Object]" uiversion="v2" style="display: none;"><ul class="_video_op_list_96mx8_17"><li class="_op_item_96mx8_39 _download_96mx8_45"><span class="_op_icon_96mx8_25"></span><span class="_op_text_96mx8_31">下载</span></li><li class="_op_item_96mx8_39 _screen_96mx8_51"><span class="_op_icon_96mx8_25"></span><span class="_op_text_96mx8_31">投屏</span></li></ul></div></div><div class="badge_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED _badge_wrapper_eor63_1" id="badge_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED"><div class="_logo_eor63_20"></div><span class="_line_eor63_27"></span><div class="_text_wrapper_eor63_33"><span class="_download_icon_eor63_39"></span><span class="_text_eor63_33">高速下载</span></div></div><!----><!----><!----><!----><div class="_container_wq4mj_4"><!----></div></div></div></html>