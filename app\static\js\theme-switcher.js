/**
 * 增强版主题切换器 - 完整功能实现
 * 支持主题收藏、预览、自动切换等高级功能
 */

class EnhancedThemeSwitcher {
    constructor() {
        this.currentTheme = localStorage.getItem('user-theme') || 'primary';
        this.favoriteThemes = JSON.parse(localStorage.getItem('favorite-themes') || '[]');
        this.previewMode = false;
        this.previewTimer = null;
        this.autoThemeEnabled = localStorage.getItem('auto-theme') === 'true';

        this.init();
    }

    init() {
        console.log('增强版主题切换器加载完成');

        // 应用保存的主题
        this.applyTheme(this.currentTheme);

        // 更新活动状态
        this.updateActiveTheme();

        // 更新收藏状态
        this.updateFavoriteStates();

        // 绑定事件
        this.bindEvents();

        // 设置自动主题切换
        if (this.autoThemeEnabled) {
            this.setupAutoTheme();
        }

        console.log('当前主题:', this.currentTheme);
    }

    bindEvents() {
        // 主题选项点击事件
        document.addEventListener('click', (e) => {
            const themeOption = e.target.closest('.theme-option');
            const favoriteBtn = e.target.closest('.theme-favorite');
            const previewToggle = e.target.closest('#themePreviewToggle');
            const autoToggle = e.target.closest('#autoThemeToggle');
            const statsBtn = e.target.closest('#themeStatsBtn');
            const exportBtn = e.target.closest('#themeExportBtn');
            const importBtn = e.target.closest('#themeImportBtn');

            if (favoriteBtn && themeOption) {
                // 收藏功能
                e.preventDefault();
                e.stopPropagation();
                this.toggleFavorite(themeOption.getAttribute('data-theme'));
            } else if (themeOption) {
                // 主题切换
                e.preventDefault();
                const theme = themeOption.getAttribute('data-theme');

                if (this.previewMode) {
                    this.previewTheme(theme);
                } else {
                    this.switchTheme(theme);
                }
            } else if (previewToggle) {
                // 预览模式切换
                e.preventDefault();
                this.togglePreviewMode();
            } else if (autoToggle) {
                // 自动主题切换
                e.preventDefault();
                this.toggleAutoTheme();
            } else if (statsBtn) {
                // 主题统计
                e.preventDefault();
                this.showThemeStats();
            } else if (exportBtn) {
                // 导出设置
                e.preventDefault();
                this.exportSettings();
            } else if (importBtn) {
                // 导入设置
                e.preventDefault();
                document.getElementById('themeImportFile').click();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.altKey) {
                switch(e.key) {
                    case 't':
                        e.preventDefault();
                        this.showThemePanel();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.togglePreviewMode();
                        break;
                }
            }
        });

        // 文件导入事件
        const importFile = document.getElementById('themeImportFile');
        if (importFile) {
            importFile.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.importSettings(file);
                    e.target.value = ''; // 清空文件选择
                }
            });
        }
    }

    applyTheme(theme, isPreview = false) {
        // 添加切换动画
        document.body.style.transition = 'all 0.3s ease';
        document.documentElement.style.transition = 'all 0.3s ease';

        // 应用主题
        document.documentElement.setAttribute('data-theme', theme);
        document.body.setAttribute('data-theme', theme);

        // 更新导航栏
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.className = navbar.className.replace(/bg-\w+/g, '');
            navbar.classList.add(`bg-${theme}`);
        }

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme, isPreview }
        }));

        if (!isPreview) {
            this.currentTheme = theme;
            localStorage.setItem('user-theme', theme);
            this.updateActiveTheme();
        }

        // 移除过渡效果
        setTimeout(() => {
            document.body.style.transition = '';
            document.documentElement.style.transition = '';
        }, 300);

        console.log(`主题${isPreview ? '预览' : '切换'}完成:`, theme);
    }

    switchTheme(theme) {
        this.applyTheme(theme);

        // 关闭下拉菜单
        const dropdown = document.querySelector('.theme-switcher-panel');
        if (dropdown) {
            dropdown.classList.remove('show');
        }

        // 显示切换成功提示
        this.showNotification(`已切换到 ${this.getThemeName(theme)} 主题`, 'success');
    }

    previewTheme(theme) {
        this.applyTheme(theme, true);

        // 清除之前的预览定时器
        if (this.previewTimer) {
            clearTimeout(this.previewTimer);
        }

        // 3秒后恢复原主题
        this.previewTimer = setTimeout(() => {
            this.applyTheme(this.currentTheme);
            this.showNotification('预览结束，已恢复原主题', 'info');
        }, 3000);

        this.showNotification(`正在预览 ${this.getThemeName(theme)} 主题`, 'info');
    }

    toggleFavorite(theme) {
        const index = this.favoriteThemes.indexOf(theme);

        if (index > -1) {
            this.favoriteThemes.splice(index, 1);
            this.showNotification(`已取消收藏 ${this.getThemeName(theme)}`, 'info');
        } else {
            this.favoriteThemes.push(theme);
            this.showNotification(`已收藏 ${this.getThemeName(theme)}`, 'success');
        }

        localStorage.setItem('favorite-themes', JSON.stringify(this.favoriteThemes));
        this.updateFavoriteStates();
    }

    togglePreviewMode() {
        this.previewMode = !this.previewMode;
        const button = document.getElementById('themePreviewToggle');

        if (this.previewMode) {
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-primary');
            this.showNotification('预览模式已开启', 'info');
        } else {
            button.classList.remove('btn-primary');
            button.classList.add('btn-outline-primary');
            this.showNotification('预览模式已关闭', 'info');

            // 清除预览定时器
            if (this.previewTimer) {
                clearTimeout(this.previewTimer);
                this.previewTimer = null;
            }
        }
    }

    toggleAutoTheme() {
        this.autoThemeEnabled = !this.autoThemeEnabled;
        localStorage.setItem('auto-theme', this.autoThemeEnabled.toString());

        const button = document.getElementById('autoThemeToggle');

        if (this.autoThemeEnabled) {
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-secondary');
            this.setupAutoTheme();
            this.showNotification('自动主题切换已开启', 'success');
        } else {
            button.classList.remove('btn-secondary');
            button.classList.add('btn-outline-secondary');
            this.showNotification('自动主题切换已关闭', 'info');
        }
    }

    setupAutoTheme() {
        if (!this.autoThemeEnabled) return;

        const checkTime = () => {
            const hour = new Date().getHours();
            let autoTheme;

            if (hour >= 6 && hour < 12) {
                autoTheme = 'minimal-dawn'; // 晨曦
            } else if (hour >= 12 && hour < 18) {
                autoTheme = 'primary'; // 日间
            } else if (hour >= 18 && hour < 22) {
                autoTheme = 'soft-morandi'; // 黄昏
            } else {
                autoTheme = 'dark-neon'; // 夜间
            }

            if (autoTheme !== this.currentTheme) {
                this.switchTheme(autoTheme);
                this.showNotification(`自动切换到 ${this.getThemeName(autoTheme)} 主题`, 'info');
            }
        };

        // 立即检查一次
        checkTime();

        // 每小时检查一次
        setInterval(checkTime, 60 * 60 * 1000);
    }

    updateActiveTheme() {
        document.querySelectorAll('.theme-option').forEach(option => {
            const theme = option.getAttribute('data-theme');
            if (theme === this.currentTheme) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
    }

    updateFavoriteStates() {
        document.querySelectorAll('.theme-option').forEach(option => {
            const theme = option.getAttribute('data-theme');
            const favoriteBtn = option.querySelector('.theme-favorite');

            if (favoriteBtn) {
                if (this.favoriteThemes.includes(theme)) {
                    favoriteBtn.style.color = '#ffc107';
                    favoriteBtn.style.opacity = '1';
                    option.classList.add('favorited');
                } else {
                    favoriteBtn.style.color = '#6c757d';
                    favoriteBtn.style.opacity = '0.5';
                    option.classList.remove('favorited');
                }
            }
        });
    }

    showThemePanel() {
        const dropdown = document.getElementById('themeDropdown');
        if (dropdown) {
            dropdown.click();
        }
    }

    getThemeName(theme) {
        const themeNames = {
            'primary': '海洋蓝',
            'secondary': '现代灰',
            'success': '自然绿',
            'warning': '活力橙',
            'info': '优雅紫',
            'danger': '深邃红',
            'classic-neutral': '经典中性风',
            'modern-neutral': '现代中性风',
            'noble-elegant': '贵族典雅风',
            'royal-solemn': '皇室庄重风',
            'deep-sea-tech': '深海科技蓝',
            'soft-morandi': '柔光莫兰迪',
            'minimal-dawn': '极简晨曦',
            'dark-neon': '暗夜霓虹',
            'nature-eco': '自然生态绿'
        };
        return themeNames[theme] || theme;
    }

    showThemeStats() {
        if (window.advancedThemeManager) {
            const stats = window.advancedThemeManager.getThemeUsageStats();
            const recommended = window.advancedThemeManager.recommendTheme();

            let statsHtml = '<div style="max-width: 300px;">';
            statsHtml += '<h6 style="margin-bottom: 15px; color: var(--theme-primary);">📊 主题使用统计</h6>';

            if (stats.length > 0) {
                stats.slice(0, 5).forEach(stat => {
                    statsHtml += `
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 6px 8px; background: rgba(0,0,0,0.05); border-radius: 4px;">
                            <span style="font-size: 0.9rem;">${this.getThemeName(stat.theme)}</span>
                            <span style="font-size: 0.8rem; color: var(--theme-primary); font-weight: bold;">${stat.percentage}%</span>
                        </div>
                    `;
                });

                statsHtml += `<div style="margin-top: 15px; padding: 8px; background: rgba(var(--theme-primary-rgb), 0.1); border-radius: 6px; text-align: center;">`;
                statsHtml += `<small style="color: var(--theme-primary);">💡 推荐主题：${this.getThemeName(recommended)}</small>`;
                statsHtml += `</div>`;
            } else {
                statsHtml += '<p style="text-align: center; color: #666; font-size: 0.9rem;">暂无使用数据</p>';
            }

            statsHtml += '</div>';

            // 使用SweetAlert2显示统计信息（如果可用）
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    html: statsHtml,
                    showConfirmButton: true,
                    confirmButtonText: '应用推荐主题',
                    showCancelButton: true,
                    cancelButtonText: '关闭',
                    customClass: {
                        popup: 'theme-stats-popup'
                    }
                }).then((result) => {
                    if (result.isConfirmed && recommended) {
                        this.switchTheme(recommended);
                    }
                });
            } else {
                // 简单的模态框
                const modal = document.createElement('div');
                modal.innerHTML = `
                    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div style="background: white; padding: 20px; border-radius: 12px; max-width: 400px; width: 90%;">
                            ${statsHtml}
                            <div style="margin-top: 15px; text-align: center;">
                                <button onclick="this.closest('[style*=\"position: fixed\"]').remove(); enhancedThemeSwitcher.switchTheme('${recommended}')" style="background: var(--theme-primary); color: white; border: none; padding: 8px 16px; border-radius: 6px; margin-right: 8px; cursor: pointer;">应用推荐</button>
                                <button onclick="this.closest('[style*=\"position: fixed\"]').remove()" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">关闭</button>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }
        } else {
            this.showNotification('统计功能暂不可用', 'warning');
        }
    }

    exportSettings() {
        if (window.advancedThemeManager) {
            window.advancedThemeManager.exportThemeSettings();
        } else {
            // 简单导出
            const settings = {
                currentTheme: this.currentTheme,
                favoriteThemes: this.favoriteThemes,
                autoThemeEnabled: this.autoThemeEnabled,
                exportDate: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'theme-settings.json';
            a.click();
            URL.revokeObjectURL(url);

            this.showNotification('主题设置已导出', 'success');
        }
    }

    importSettings(file) {
        if (window.advancedThemeManager) {
            window.advancedThemeManager.importThemeSettings(file);
        } else {
            // 简单导入
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const settings = JSON.parse(e.target.result);

                    if (settings.currentTheme) {
                        this.switchTheme(settings.currentTheme);
                    }

                    if (settings.favoriteThemes) {
                        this.favoriteThemes = settings.favoriteThemes;
                        localStorage.setItem('favorite-themes', JSON.stringify(settings.favoriteThemes));
                        this.updateFavoriteStates();
                    }

                    if (typeof settings.autoThemeEnabled === 'boolean') {
                        this.autoThemeEnabled = settings.autoThemeEnabled;
                        localStorage.setItem('auto-theme', settings.autoThemeEnabled.toString());

                        const button = document.getElementById('autoThemeToggle');
                        if (button) {
                            if (this.autoThemeEnabled) {
                                button.classList.remove('btn-outline-secondary');
                                button.classList.add('btn-secondary');
                            } else {
                                button.classList.remove('btn-secondary');
                                button.classList.add('btn-outline-secondary');
                            }
                        }
                    }

                    this.showNotification('主题设置已导入', 'success');

                } catch (error) {
                    this.showNotification('导入失败：文件格式错误', 'error');
                }
            };
            reader.readAsText(file);
        }
    }

    showNotification(message, type = 'info') {
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            // 简单的原生通知
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--theme-primary);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;

            document.body.appendChild(notification);
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    }
}

// 初始化增强版主题切换器
let enhancedThemeSwitcher;

document.addEventListener('DOMContentLoaded', function() {
    enhancedThemeSwitcher = new EnhancedThemeSwitcher();

    // 导出到全局作用域供其他脚本使用
    window.themeSwitcher = enhancedThemeSwitcher;
    window.switchTheme = (theme) => enhancedThemeSwitcher.switchTheme(theme);
    window.previewTheme = (theme) => enhancedThemeSwitcher.previewTheme(theme);
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
